import React from 'react'
import { type GuidePageProps } from '@ht/chatui'
import { chatLogo } from '@src/common/images'
import Navbar from '../../Navbar'
import './style.less'

const GuidePage = React.forwardRef<HTMLDivElement, GuidePageProps>(
  (props, ref) => {
    const { navbar } = props

    return (
      <div ref={ref} className="guide-page">
        <Navbar {...navbar} />
        <div className="chat-content">
          <div className="center-content">
            <img src={chatLogo} className="chat-logo" />
            <div className="first-title">Hi～我是Web助手</div>
            <div className="sub-title">作为您的智能助手，可以为您答疑解惑</div>
          </div>
        </div>
      </div>
    )
  }
)

export default GuidePage
