@import '../../../../common/css/var.less';

.guide-page {
  display: flex;
  flex-direction: column;
  color: @body-color;
  font-family: @font-family-base;
  line-height: @line-height-base;
  -webkit-tap-highlight-color: transparent;
  background: @guide-page-bg;
  position: relative;
  border-radius: inherit;
  flex: 1;
  .chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .center-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .chat-logo {
        width: 98px;
        height: 98px;
      }
      .first-title {
        margin-top: 25px;
        margin-bottom: 12px;
        font-size: 20px;
        font-weight: bold;
        color: @guide-page-title-color;
        line-height: 27px;
      }

      .sub-title {
        font-size: 14px;
        color: @guide-page-subtitle-color;
        line-height: 19px;
      }
    }
  }
}
