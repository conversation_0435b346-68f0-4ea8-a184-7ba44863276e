import React, { useEffect, useState } from 'react';
import { message } from 'antd';

interface SelectionHandlerProps {
  onTextSelected?: (text: string, operation: string) => void;
}

const SelectionHandler: React.FC<SelectionHandlerProps> = ({ onTextSelected }) => {
  useEffect(() => {
    // 监听来自背景脚本的消息
    const messageListener = (message: any) => {
      if (message.type === 'SELECTED_TEXT' && message.text) {
        // 当收到选中的文本时，通知父组件
        onTextSelected?.(message.text, '');
      } else if (message.type === 'PROCESS_TEXT' && message.text) {
        // 当收到处理文本的请求时，通知父组件
        onTextSelected?.(message.text, message.operation || '');
      }
    };

    chrome.runtime.onMessage.addListener(messageListener);

    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, [onTextSelected]);

  // 这个组件不渲染任何内容，只是作为消息处理器
  return null;
};

export default SelectionHandler;