import React from 'react';
import { createRoot, Root } from 'react-dom/client';
import SelectionBarComponent from '../components/SelectionBar/index';
import FloatingButtonComponent from '../components/FloatingButton/index';

/**
 * Web Assistant 主管理器
 * 统一管理划词工具栏和浮动按钮
 */
class WebAssistantManager {
  private mainContainer: HTMLDivElement | null = null;
  private shadowRoot: ShadowRoot | null = null;
  private selectionBarContainer: HTMLDivElement | null = null;
  private floatingButtonContainer: HTMLDivElement | null = null;
  private selectionBarRoot: Root | null = null;
  private floatingButtonRoot: Root | null = null;
  
  private selectedText: string = '';
  private isSelectionBarVisible: boolean = false;
  private isFloatingButtonVisible: boolean = true;

  constructor() {
    this.init();
  }

  private init(): void {
    console.log('WebAssistantManager: Initializing...');
    this.createMainContainer();
    this.initEventListeners();
    this.initFloatingButton();
  }

  /**
   * 创建主容器和 Shadow DOM
   */
  private createMainContainer(): void {
    console.log('WebAssistantManager: Creating main container...');

    // 检查是否已经存在主容器，避免重复创建
    const existingContainer = document.getElementById('web-assistant-main-container');
    if (existingContainer) {
      console.log('WebAssistantManager: Main container already exists, skipping creation');
      return;
    }

    // 创建主容器
    this.mainContainer = document.createElement('div');
    this.mainContainer.id = 'web-assistant-main-container';
    this.mainContainer.className = 'web-assistant-container';
    
    // 创建 Shadow Root
    this.shadowRoot = this.mainContainer.attachShadow({ mode: 'open' });
    console.log('WebAssistantManager: Shadow root created');
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = this.getStyles();
    this.shadowRoot.appendChild(style);
    
    // 创建划词工具栏容器
    this.selectionBarContainer = document.createElement('div');
    this.selectionBarContainer.id = 'web-assistant-selection-bar';
    this.selectionBarContainer.className = 'web-assistant-selection-bar-container';
    this.shadowRoot.appendChild(this.selectionBarContainer);

    // 创建浮动按钮容器
    this.floatingButtonContainer = document.createElement('div');
    this.floatingButtonContainer.id = 'web-assistant-floating-button';
    this.floatingButtonContainer.className = 'web-assistant-floating-button-container';
    this.shadowRoot.appendChild(this.floatingButtonContainer);
    
    // 添加到页面
    document.body.appendChild(this.mainContainer);
    console.log('WebAssistantManager: Main container added to body');
  }

  /**
   * 初始化事件监听器
   */
  private initEventListeners(): void {
    // 监听文本选择
    document.addEventListener('mouseup', this.handleMouseUp);
    document.addEventListener('keyup', this.handleKeyUp);
    
    // 监听点击事件（用于隐藏工具栏）
    document.addEventListener('click', this.handleDocumentClick);
    
    // 监听选择变化
    document.addEventListener('selectionchange', this.handleSelectionChange);
  }

  /**
   * 处理鼠标抬起事件
   */
  private handleMouseUp = (event: MouseEvent): void => {
    // 延迟处理，确保选择已完成
    setTimeout(() => {
      this.checkSelection(event);
    }, 10);
  };

  /**
   * 处理键盘事件
   */
  private handleKeyUp = (event: KeyboardEvent): void => {
    // 延迟处理，确保选择已完成
    setTimeout(() => {
      this.checkSelection();
    }, 10);
  };

  /**
   * 处理选择变化事件
   */
  private handleSelectionChange = (): void => {
    // 如果没有选择文本，隐藏工具栏
    const selection = window.getSelection();
    if (!selection || selection.toString().trim() === '') {
      this.hideSelectionBar();
    }
  };

  /**
   * 处理文档点击事件
   */
  private handleDocumentClick = (event: MouseEvent): void => {
    // 如果点击的不是工具栏内部，且工具栏可见，则隐藏工具栏
    if (this.isSelectionBarVisible && this.selectionBarContainer) {
      const target = event.target as Node;
      if (!this.selectionBarContainer.contains(target)) {
        // 检查是否还有选中的文本
        const selection = window.getSelection();
        if (!selection || selection.toString().trim() === '') {
          this.hideSelectionBar();
        }
      }
    }
  };

  /**
   * 检查当前选择
   */
  private checkSelection(event?: MouseEvent): void {
    const selection = window.getSelection();
    if (!selection) return;
    
    const newSelectedText = selection.toString().trim();
    console.log('WebAssistantManager: Checking selection:', newSelectedText);
    
    if (newSelectedText && newSelectedText !== this.selectedText) {
      this.selectedText = newSelectedText;
      this.showSelectionBar(event);
    } else if (!newSelectedText && this.isSelectionBarVisible) {
      this.hideSelectionBar();
    }
  }

  /**
   * 显示划词工具栏
   */
  private showSelectionBar(event?: MouseEvent): void {
    if (!this.selectedText || !this.selectionBarContainer) return;
    
    console.log('WebAssistantManager: Showing selection bar for text:', this.selectedText);
    
    // 创建 React root（如果还没有）
    if (!this.selectionBarRoot) {
      this.selectionBarRoot = createRoot(this.selectionBarContainer);
    }
    
    // 渲染组件
    this.selectionBarRoot.render(
      React.createElement(SelectionBarComponent, {
        selectedText: this.selectedText,
        onAction: this.handleSelectionBarAction,
        onClose: this.hideSelectionBar
      })
    );
    
    // 定位工具栏
    this.positionSelectionBar();
    
    // 显示工具栏
    this.selectionBarContainer.classList.add('show');
    this.isSelectionBarVisible = true;
    
    console.log('WebAssistantManager: Selection bar shown');
  }

  /**
   * 隐藏划词工具栏
   */
  private hideSelectionBar = (): void => {
    if (this.selectionBarContainer && this.isSelectionBarVisible) {
      this.selectionBarContainer.classList.remove('show');
      this.isSelectionBarVisible = false;
      this.selectedText = '';
      console.log('WebAssistantManager: Selection bar hidden');
    }
  };

  /**
   * 定位划词工具栏
   */
  private positionSelectionBar(): void {
    if (!this.selectionBarContainer) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    console.log('WebAssistantManager: Positioning selection bar at:', rect);

    // 计算悬浮球的位置，让它出现在选中文本的下方
    const offsetY = 8; // 垂直偏移，避免紧贴文本
    const barWidth = 300; // 估算的悬浮球宽度
    const barHeight = 50; // 估算的悬浮球高度

    // 在shadow DOM中，主容器使用固定定位，选择栏容器使用绝对定位
    // rect已经是相对于视口的位置，直接使用即可
    let left = rect.left;
    let top = rect.bottom + offsetY;

    // 确保悬浮球不会超出视窗右边界
    const viewportWidth = window.innerWidth;
    if (left + barWidth > viewportWidth) {
      // 如果超出右边界，调整到选中文本的右边界对齐
      left = rect.right - barWidth;
    }

    // 确保悬浮球不会超出视窗下边界
    const viewportHeight = window.innerHeight;
    if (top + barHeight > viewportHeight) {
      // 如果超出下边界，显示在选中文本的上方
      top = rect.top - barHeight - offsetY;
    }

    // 确保不会超出左边界和上边界
    left = Math.max(5, left);
    top = Math.max(5, top);

    this.selectionBarContainer.style.left = `${left}px`;
    this.selectionBarContainer.style.top = `${top}px`;
  }

  /**
   * 初始化浮动按钮
   */
  private initFloatingButton(): void {
    if (!this.floatingButtonContainer) return;
    
    console.log('WebAssistantManager: Initializing floating button...');
    
    // 创建 React root
    this.floatingButtonRoot = createRoot(this.floatingButtonContainer);
    
    // 渲染组件
    this.floatingButtonRoot.render(
      React.createElement(FloatingButtonComponent, {
        onAction: this.handleFloatingButtonAction
      })
    );
    
    console.log('WebAssistantManager: Floating button initialized');
  }

  /**
   * 处理划词工具栏动作
   */
  private handleSelectionBarAction = (action: string): void => {
    console.log('WebAssistantManager: Selection bar action:', action, 'with text:', this.selectedText);
    
    switch (action) {
      case 'summary':
        this.handleSummary();
        break;
      case 'translate':
        this.handleTranslate();
        break;
      case 'abbreviate':
      case 'expand':
      case 'polish':
      case 'correct':
        this.handleTextProcessing(action);
        break;
      case 'open-panel':
        this.handleOpenPanel();
        break;
      default:
        console.log('WebAssistantManager: Unknown action:', action);
    }
  };

  /**
   * 处理浮动按钮动作
   */
  private handleFloatingButtonAction = (action: string): void => {
    console.log('WebAssistantManager: Floating button action:', action);
    
    switch (action) {
      case 'open-panel':
        this.handleOpenPanel();
        break;
      case 'quick-translate':
        this.handleQuickTranslate();
        break;
      case 'quick-summary':
        this.handleQuickSummary();
        break;
      case 'settings':
        this.handleSettings();
        break;
      default:
        console.log('WebAssistantManager: Unknown floating button action:', action);
    }
  };

  /**
   * 处理总结功能
   */
  private handleSummary(): void {
    console.log('WebAssistantManager: Handling summary for:', this.selectedText);
    // TODO: 实现总结功能
  }

  /**
   * 处理翻译功能
   */
  private handleTranslate(): void {
    console.log('WebAssistantManager: Handling translate for:', this.selectedText);
    // TODO: 实现翻译功能
  }

  /**
   * 处理文本处理功能
   */
  private handleTextProcessing(action: string): void {
    console.log('WebAssistantManager: Handling text processing:', action, 'for:', this.selectedText);
    // TODO: 实现文本处理功能
  }

  /**
   * 处理打开面板
   */
  private handleOpenPanel(): void {
    console.log('WebAssistantManager: Opening panel...');
    // TODO: 实现打开面板功能
  }

  /**
   * 处理快速翻译
   */
  private handleQuickTranslate(): void {
    console.log('WebAssistantManager: Quick translate...');
    // TODO: 实现快速翻译功能
  }

  /**
   * 处理快速总结
   */
  private handleQuickSummary(): void {
    console.log('WebAssistantManager: Quick summary...');
    // TODO: 实现快速总结功能
  }

  /**
   * 处理设置
   */
  private handleSettings(): void {
    console.log('WebAssistantManager: Opening settings...');
    // TODO: 实现设置功能
  }

  /**
   * 获取样式
   */
  private getStyles(): string {
    return `
      /* Web Assistant 主样式文件 */

      /* 重置样式 */
      * {
        box-sizing: border-box;
      }

      /* 主容器样式 */
      .web-assistant-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        z-index: 2147483647;
        pointer-events: none;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }

      /* 划词工具栏容器 */
      .web-assistant-selection-bar-container {
        position: absolute;
        display: none;
        pointer-events: auto;
      }

      .web-assistant-selection-bar-container.show {
        display: block;
      }

      /* 浮动按钮容器 */
      .web-assistant-floating-button-container {
        position: fixed;
        pointer-events: auto;
      }

      /* 确保所有子元素都有正确的指针事件 */
      #web-assistant-selection-bar,
      #web-assistant-floating-button {
        pointer-events: auto;
      }
    `;
  }
}

// 防止重复初始化
declare global {
  interface Window {
    webAssistantManagerInitialized?: boolean;
  }
}

// 初始化 Web Assistant Manager
if (!window.webAssistantManagerInitialized) {
  console.log('WebAssistantManager: Starting initialization...');
  window.webAssistantManagerInitialized = true;
  new WebAssistantManager();
} else {
  console.log('WebAssistantManager: Already initialized, skipping...');
}
