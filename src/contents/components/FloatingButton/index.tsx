import React, { useState, useRef, useEffect } from 'react';
import { browser } from 'webextension-polyfill-ts';

interface FloatingButtonProps {
  onAction: (action: string) => void;
}

const FloatingButton: React.FC<FloatingButtonProps> = ({ onAction }) => {
  const [position, setPosition] = useState({ x: window.innerWidth - 60, y: window.innerHeight / 2 });
  const [isDragging, setIsDragging] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const buttonRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    const rect = buttonRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newX = Math.max(0, Math.min(window.innerWidth - 50, e.clientX - dragOffset.x));
      const newY = Math.max(0, Math.min(window.innerHeight - 50, e.clientY - dragOffset.y));
      setPosition({ x: newX, y: newY });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isDragging) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleAction = (action: string) => {
    onAction(action);
    setIsExpanded(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  return (
    <div
      ref={buttonRef}
      className="web-assistant-floating-button"
      style={{
        position: 'fixed',
        left: `${position.x}px`,
        top: `${position.y}px`,
        zIndex: 2147483647,
        cursor: isDragging ? 'grabbing' : 'grab'
      }}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
    >
      <div
        className="web-assistant-floating-main"
        style={{
          width: '50px',
          height: '50px',
          borderRadius: '50%',
          backgroundColor: '#007bff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 4px 15px rgba(0, 123, 255, 0.3)',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          animation: 'pulse 2s infinite'
        }}
      >
        <img 
          src={browser.runtime.getURL('assets/icon.png')} 
          alt="AI助手"
          style={{
            width: '24px',
            height: '24px',
            filter: 'brightness(0) invert(1)'
          }}
        />
      </div>

      {isExpanded && (
        <div
          className="web-assistant-floating-menu"
          style={{
            position: 'absolute',
            bottom: '60px',
            right: '0',
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
            padding: '8px',
            minWidth: '150px',
            opacity: isExpanded ? 1 : 0,
            transform: isExpanded ? 'translateY(0) scale(1)' : 'translateY(10px) scale(0.9)',
            transition: 'all 0.2s ease'
          }}
        >
          <div
            className="web-assistant-floating-menu-item"
            onClick={() => handleAction('open-panel')}
            style={{
              padding: '10px 12px',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'background-color 0.2s ease',
              fontSize: '14px'
            }}
          >
            🤖 AI 助手
          </div>
          <div
            className="web-assistant-floating-menu-item"
            onClick={() => handleAction('quick-translate')}
            style={{
              padding: '10px 12px',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'background-color 0.2s ease',
              fontSize: '14px'
            }}
          >
            🌐 快速翻译
          </div>
          <div
            className="web-assistant-floating-menu-item"
            onClick={() => handleAction('quick-summary')}
            style={{
              padding: '10px 12px',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'background-color 0.2s ease',
              fontSize: '14px'
            }}
          >
            📝 页面总结
          </div>
          <div
            className="web-assistant-floating-menu-item"
            onClick={() => handleAction('settings')}
            style={{
              padding: '10px 12px',
              cursor: 'pointer',
              borderRadius: '4px',
              transition: 'background-color 0.2s ease',
              fontSize: '14px'
            }}
          >
            ⚙️ 设置
          </div>
        </div>
      )}

      <style>{`
        @keyframes pulse {
          0% {
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
          }
          50% {
            box-shadow: 0 4px 25px rgba(0, 123, 255, 0.5);
          }
          100% {
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
          }
        }
      `}</style>
    </div>
  );
};

export default FloatingButton;
