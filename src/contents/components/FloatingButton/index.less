.web-assistant-floating-button {
  position: fixed;
  z-index: 2147483647;
  user-select: none;

  .web-assistant-floating-main {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #007bff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    }

    &:active {
      transform: scale(0.95);
    }

    img {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }

  .web-assistant-floating-menu {
    position: absolute;
    bottom: 60px;
    right: 0;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 8px;
    min-width: 150px;
    opacity: 0;
    transform: translateY(10px) scale(0.9);
    transition: all 0.2s ease;

    &.show {
      opacity: 1;
      transform: translateY(0) scale(1);
    }

    .web-assistant-floating-menu-item {
      padding: 10px 12px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.2s ease;
      font-size: 14px;
      color: #333;

      &:hover {
        background-color: #f0f0f0;
        color: #007bff;
      }

      &:active {
        background-color: #e0e0e0;
      }
    }

    // 菜单箭头
    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      right: 20px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid white;
      filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.1));
    }
  }

  // 拖拽状态
  &.dragging {
    .web-assistant-floating-main {
      cursor: grabbing;
      transform: scale(1.05);
    }
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  }
  50% {
    box-shadow: 0 4px 25px rgba(0, 123, 255, 0.5);
  }
  100% {
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .web-assistant-floating-button {
    .web-assistant-floating-main {
      width: 45px;
      height: 45px;

      img {
        width: 20px;
        height: 20px;
      }
    }

    .web-assistant-floating-menu {
      min-width: 130px;

      .web-assistant-floating-menu-item {
        padding: 8px 10px;
        font-size: 13px;
      }
    }
  }
}
