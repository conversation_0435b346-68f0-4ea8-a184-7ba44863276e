import React, { useState } from 'react';
import { browser } from 'webextension-polyfill-ts';
import './index.less';

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleAction = (action: string) => {
    onAction(action);
    if (action !== 'open-panel') {
      onClose();
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  return (
    <div className="web-assistant-selection-container">
      {/* 箭头指向选中文本 */}
      <div className="web-assistant-selection-arrow" />

      <div className="web-assistant-selection-bar">
      <div
        className="web-assistant-selection-icon"
        onClick={() => handleAction('open-panel')}
        title="AI助手"
      >
        <img
          src={browser.runtime.getURL('assets/icon.png')}
          alt="AI"
        />
      </div>

      <div
        className="web-assistant-selection-button"
        onClick={() => handleAction('polish')}
      >
        <img
          src={browser.runtime.getURL('assets/polish-icon.svg')}
          alt="润色"
        />
        <span>润色</span>
      </div>

      <div
        className="web-assistant-selection-button"
        onClick={() => handleAction('translate')}
      >
        <img
          src={browser.runtime.getURL('assets/translate-icon.svg')}
          alt="翻译"
        />
        <span>翻译</span>
      </div>

      <div className="web-assistant-selection-more">
        <div
          className="web-assistant-selection-dots"
          onClick={toggleDropdown}
        >
          ⋮
        </div>
        {showDropdown && (
          <div className="web-assistant-selection-dropdown show">
            <div
              className="web-assistant-selection-dropdown-item"
              onClick={() => handleAction('summary')}
            >
              总结
            </div>
            <div
              className="web-assistant-selection-dropdown-item"
              onClick={() => handleAction('abbreviate')}
            >
              缩写
            </div>
            <div
              className="web-assistant-selection-dropdown-item"
              onClick={() => handleAction('expand')}
            >
              扩写
            </div>
            <div
              className="web-assistant-selection-dropdown-item"
              onClick={() => handleAction('correct')}
            >
              修正拼写和语义
            </div>
          </div>
        )}
      </div>

      {/* 分隔线 */}
      <div className="web-assistant-selection-divider" />

      <div
        className="web-assistant-selection-close"
        onClick={onClose}
        title="关闭"
      >
        ×
      </div>
      </div>
    </div>
  );
};

export default SelectionBar;
