import React, { useState } from 'react';
import { browser } from 'webextension-polyfill-ts';

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleAction = (action: string) => {
    onAction(action);
    if (action !== 'open-panel') {
      onClose();
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  return (
    <div style={{ position: 'relative' }}>
      {/* 箭头指向选中文本 */}
      <div
        style={{
          position: 'absolute',
          top: '-8px',
          left: '20px',
          width: '0',
          height: '0',
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderBottom: '8px solid white',
          filter: 'drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1))'
        }}
      />
      
      <div
        className="web-assistant-selection-bar"
        style={{
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          backgroundColor: 'white',
          borderRadius: '20px',
          boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)',
          padding: '8px 12px',
          zIndex: 9999,
          fontFamily: 'sans-serif',
          fontSize: '16px',
          border: '1px solid #ddd',
          opacity: 1,
          transform: 'translateY(0)',
          width: 'fit-content'
        }}
      >
      <div
        className="web-assistant-selection-icon"
        onClick={() => handleAction('open-panel')}
        title="AI助手"
        style={{
          display: 'flex',
          alignItems: 'center',
          width: '24px',
          height: '24px',
          margin: '0 6px',
          cursor: 'pointer'
        }}
      >
        <img
          src={browser.runtime.getURL('assets/icon.png')}
          alt="AI"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain' as const
          }}
        />
      </div>

      <div
        className="web-assistant-selection-button"
        onClick={() => handleAction('polish')}
        style={{
          display: 'flex',
          alignItems: 'center',
          margin: '0 6px',
          cursor: 'pointer',
          color: '#1c1c1e'
        }}
      >
        <img
          src={browser.runtime.getURL('assets/polish-icon.svg')}
          alt="润色"
          style={{
            width: '24px',
            height: '24px',
            objectFit: 'contain' as const
          }}
        />
        <span style={{ marginLeft: '4px', fontSize: '16px' }}>润色</span>
      </div>

      <div
        className="web-assistant-selection-button"
        onClick={() => handleAction('translate')}
        style={{
          display: 'flex',
          alignItems: 'center',
          margin: '0 6px',
          cursor: 'pointer',
          color: '#1c1c1e'
        }}
      >
        <img
          src={browser.runtime.getURL('assets/translate-icon.svg')}
          alt="翻译"
          style={{
            width: '24px',
            height: '24px',
            objectFit: 'contain' as const
          }}
        />
        <span style={{ marginLeft: '4px', fontSize: '16px' }}>翻译</span>
      </div>

      <div
        className="web-assistant-selection-more"
        style={{
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          margin: '0 6px',
          cursor: 'pointer'
        }}
      >
        <div
          className="web-assistant-selection-dots"
          onClick={toggleDropdown}
          style={{
            fontSize: '16px',
            color: '#1c1c1e'
          }}
        >
          ⋮
        </div>
        {showDropdown && (
          <div
            className="web-assistant-selection-dropdown show"
            style={{
              position: 'absolute',
              top: '100%',
              right: '0',
              backgroundColor: 'white',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
              minWidth: '120px',
              zIndex: 10000
            }}
          >
            <div
              className="web-assistant-selection-dropdown-item"
              onClick={() => handleAction('summary')}
              style={{
                padding: '8px 12px',
                cursor: 'pointer'
              }}
            >
              总结
            </div>
            <div
              className="web-assistant-selection-dropdown-item"
              onClick={() => handleAction('abbreviate')}
              style={{
                padding: '8px 12px',
                cursor: 'pointer'
              }}
            >
              缩写
            </div>
            <div
              className="web-assistant-selection-dropdown-item"
              onClick={() => handleAction('expand')}
              style={{
                padding: '8px 12px',
                cursor: 'pointer'
              }}
            >
              扩写
            </div>
            <div
              className="web-assistant-selection-dropdown-item"
              onClick={() => handleAction('correct')}
              style={{
                padding: '8px 12px',
                cursor: 'pointer'
              }}
            >
              修正拼写和语义
            </div>
          </div>
        )}
      </div>

      {/* 分隔线 */}
      <div
        style={{
          width: '1px',
          height: '20px',
          backgroundColor: '#ccc',
          margin: '0 6px'
        }}
      />

      <div
        className="web-assistant-selection-close"
        onClick={onClose}
        title="关闭"
        style={{
          display: 'flex',
          alignItems: 'center',
          margin: '0 6px',
          cursor: 'pointer',
          fontSize: '20px',
          fontWeight: 'bold',
          color: '#888'
        }}
      >
        ×
      </div>
      </div>
    </div>
  );
};

export default SelectionBar;
