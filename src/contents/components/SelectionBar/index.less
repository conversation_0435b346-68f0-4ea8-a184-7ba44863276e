.web-assistant-selection-bar {
  position: absolute;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 8px 12px;
  z-index: 9999;
  font-family: sans-serif;
  font-size: 16px;
  transition: opacity 0.2s ease, transform 0.2s ease;
  border: 1px solid #ddd;
  opacity: 1;
  transform: translateY(0);
  width: fit-content;

  .web-assistant-selection-icon {
    display: flex;
    align-items: center;
    width: 24px;
    height: 24px;
    margin: 0 6px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .web-assistant-selection-button {
    display: flex;
    align-items: center;
    margin: 0 6px;
    cursor: pointer;
    color: #1c1c1e;
    transition: all 0.2s ease;

    img {
      width: 24px;
      height: 24px;
      object-fit: contain;
    }

    span {
      margin-left: 4px;
      font-size: 16px;
    }

    &:hover {
      opacity: 0.7;
    }
  }

  .web-assistant-selection-more {
    position: relative;
    display: flex;
    align-items: center;
    margin: 0 6px;
    cursor: pointer;

    .web-assistant-selection-dots {
      font-size: 16px;
      color: #1c1c1e;

      &:hover {
        opacity: 0.7;
      }
    }

    .web-assistant-selection-dropdown {
      position: absolute;
      top: 100%;
      right: 0;
      background-color: white;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      min-width: 120px;
      z-index: 10000;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.2s ease;

      &.show {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .web-assistant-selection-dropdown-item {
        padding: 8px 12px;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f0f0f0;
          color: #007bff;
        }

        &:first-child {
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
        }

        &:last-child {
          border-bottom-left-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }
    }
  }

  .web-assistant-selection-close {
    display: flex;
    align-items: center;
    margin: 0 6px;
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    color: #888;
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.7;
    }
  }

  // 分隔线样式
  .web-assistant-selection-divider {
    width: 1px;
    height: 20px;
    background-color: #ccc;
    margin: 0 6px;
  }
}

// 容器样式
.web-assistant-selection-container {
  position: relative;
}

// 箭头样式
.web-assistant-selection-arrow {
  position: absolute;
  top: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}
