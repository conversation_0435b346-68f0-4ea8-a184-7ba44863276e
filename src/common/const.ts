export const MessageType = {
  START_TRANSLATE: 'START_TRANSLATE', // 开始翻译
  CREATE_CONVERSATION: 'CREATE_CONVERSATION', // 创建会话
  BATCH_TRANSLATE: 'BATCH_TRANSLATE', // 批量翻译
  SINGLE_TRANSLATE: 'SINGLE_TRANSLATE', // 单个翻译
  CHECK_CONTENT_SCRIPT: 'CHECK_CONTENT_SCRIPT', // 检查content_script是否加载
}

export interface TMessage<T> {
  type: string
  data?: T
}

/**
 * 插件用到的所有服务
 */
const API_GATE_WAY =
  'http://************:8090/sourcemap-analyzer/htsc-ai-extension' // 测试环境api网关

export const SERVERS_CONFIG = {
  LOGIN_PRD: `${API_GATE_WAY}/gw-server/prd`,
  LOGIN_DEV: `${API_GATE_WAY}/gw-server/dev`,
  AGENT_GENERAL: `${API_GATE_WAY}/agent-server/general`,
  AGENT_PAGESUMMARY: `${API_GATE_WAY}/agent-server/pagesummary`,
  AGENT_TRANSLATE: `${API_GATE_WAY}/agent-server/translate`,
  CI_SERVER: `${API_GATE_WAY}/ci-server`,
}

export const ModelType = {
  general: {
    value: SERVERS_CONFIG.AGENT_GENERAL,
    label: '通用智能体',
    placeholder: '请输入您的问题',
  }, // 通用智能体
  pagesummary: {
    value: SERVERS_CONFIG.AGENT_PAGESUMMARY,
    label: '网页问答',
    placeholder: '针对当前网页内容请输入您的问题',
  }, // 网页问答智能体
  translate: {
    value: SERVERS_CONFIG.AGENT_TRANSLATE,
    label: '网页翻译',
    placeholder: '请输入内容进行翻译',
  }, // 翻译智能体
}
