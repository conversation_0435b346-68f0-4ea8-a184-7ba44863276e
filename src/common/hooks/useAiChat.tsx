import { useAppSelector } from '@src/store'
import { ModelType } from '@src/common/const'

export default () => {
  const modelType = useAppSelector((state) => state.aiChat.modelType)

  return {
    modelType,
    modelTypeLabel:
      Object.values(ModelType).find((item) => item.value === modelType)
        ?.label || '',
    isGeneral: modelType === ModelType.general.value,
    isPagesummary: modelType === ModelType.pagesummary.value,
    isTranslate: modelType === ModelType.translate.value,
  }
}
