import { createSlice } from '@reduxjs/toolkit'
import { WechatOutlined, AppstoreOutlined } from '@src/common/Icons'
import { ModelType } from '@src/common/const'

export const TAB_TYPE_CONFIG = {
  CHAR: {
    value: '聊天模式',
    Icon: WechatOutlined,
  },
  TOOLS: {
    value: '更多功能',
    Icon: AppstoreOutlined,
  },
} as const

export type TtabType =
  (typeof TAB_TYPE_CONFIG)[keyof typeof TAB_TYPE_CONFIG]['value']

export interface IAiChatState {
  tabType: TtabType
  modelType: string
}

const aiChatSlice = createSlice({
  name: 'aiChat',
  initialState: {
    tabType: TAB_TYPE_CONFIG.CHAR.value,
    modelType: ModelType.general.value,
  } as IAiChatState,
  reducers: {
    handleTabType: (state, action) => {
      state.tabType = action.payload
    },
    handleModelType: (state, action) => {
      state.modelType = action.payload
    },
  },
})

export const { handleTabType, handleModelType } = aiChatSlice.actions

export default aiChatSlice.reducer
