/**
 * 通过chrome-extension://<extension-id>/tabs/upgrade.html 访问
 */
import { useEffect, useState } from 'react'
import { Spin, message } from 'antd/es'
import { buildApi, ipConnectivity } from '@src/common/utils'
import { VersionIcon, DateIcon, ZipIcon } from '@src/common/Icons'
import { SERVERS_CONFIG } from '@src/common/const'
import './style.less'

export default () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [versionList, setVersionList] = useState([])
  const [loading, setLoading] = useState(false)

  const fetchVersionList = async () => {
    setLoading(true)
    const getVersionList = buildApi(
      'GET',
      `${SERVERS_CONFIG.CI_SERVER}/chrome-extensions/ai-extension/versions.json`,
      {
        isNeedFullResponse: true,
        timeout: 1.5 * 1000,
      }
    )
    const result: any = await getVersionList().catch((err) => err)

    if (result?.status !== 200) {
      messageApi.open({
        type: 'error',
        content: '请求失败，请重试！',
      })
      setLoading(false)
      return
    }
    setVersionList(result?.data || [])
    setLoading(false)
  }

  const handleDownload = (downloadUrl: string) => {
    // 创建隐藏的a标签
    const downloadElement = document.createElement('a')
    downloadElement.href = downloadUrl // 直接使用URL

    // 触发点击事件
    document.body.appendChild(downloadElement)
    downloadElement.click()
    document.body.removeChild(downloadElement)
    messageApi.open({
      type: 'success',
      content: '插件下载成功',
    })
  }

  useEffect(() => {
    ipConnectivity(messageApi)
    fetchVersionList()
  }, [])

  return (
    <div className="upgrade-page">
      {contextHolder}
      <div className="title">Web助手版本列表</div>
      <div className="version-list-wrap">
        <div className="header">
          <div className="version">
            <VersionIcon />
            <span className="version-name">Versions</span>
          </div>
        </div>
        {loading ? (
          <Spin
            tip="Loading"
            size="large"
            className="loading-spinner"
          ></Spin>
        ) : (
          versionList.map((versionItem) => {
            const { version, date, changeList, downloadUrl } = versionItem

            return (
              <div className="version-list-item" key={version}>
                <div className="version-num">{version}</div>
                <ul className="change-wrap">
                  {(changeList || []).map((changeItem) => {
                    return (
                      <li className="change-item" key={changeItem}>
                        {changeItem}
                      </li>
                    )
                  })}
                </ul>
                <div className="date-wrap">
                  <div className="date-box">
                    <DateIcon />
                    <span className="date">{date}</span>
                  </div>
                  <div
                    className="zip-box"
                    onClick={() => handleDownload(downloadUrl)}
                  >
                    <ZipIcon />
                    <span className="zip">zip</span>
                  </div>
                </div>
              </div>
            )
          })
        )}
      </div>
    </div>
  )
}
