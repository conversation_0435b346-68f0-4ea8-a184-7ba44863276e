/**
 * Service Worker：用来监听事件、发送消息
 */

// 点击插件图标以打开侧边栏（sidepanel）
chrome.sidePanel.setPanelBehavior({
  openPanelOnActionClick: true,
})

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 处理划词工具栏的操作
  if (message.action) {
    console.log('收到划词工具栏操作:', message.action, '选中文本:', message.text);
    
    switch (message.action) {
      case 'open-panel':
        // 打开侧边栏面板
        chrome.sidePanel.open({ windowId: sender.tab?.windowId });
        // 将选中的文本发送到侧边栏
        chrome.runtime.sendMessage({
          type: 'SELECTED_TEXT',
          text: message.text
        });
        break;
        
      case 'summary':
        // 处理总结操作
        handleSummaryAction(message.text, sender.tab?.id);
        break;
        
      case 'translate':
        // 处理翻译操作
        handleTranslateAction(message.text, sender.tab?.id);
        break;
        
      case 'abbreviate':
        // 处理缩写操作
        handleTextOperation(message.text, '缩写', sender.tab?.id);
        break;
        
      case 'expand':
        // 处理扩写操作
        handleTextOperation(message.text, '扩写', sender.tab?.id);
        break;
        
      case 'polish':
        // 处理润色操作
        handleTextOperation(message.text, '润色', sender.tab?.id);
        break;
        
      case 'correct':
        // 处理修正拼写和语义操作
        handleTextOperation(message.text, '修正拼写和语义', sender.tab?.id);
        break;
    }
  }
  
  return true; // 表示异步响应
});

// 处理总结操作
function handleSummaryAction(text: string, tabId?: number) {
  // 这里可以实现总结功能，例如调用AI API进行总结
  console.log('执行总结操作:', text);
  
  // 示例：向侧边栏发送消息，请求总结
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: '总结',
    text: text
  });
}

// 处理翻译操作
function handleTranslateAction(text: string, tabId?: number) {
  // 这里可以实现翻译功能，例如调用翻译API
  console.log('执行翻译操作:', text);
  
  // 示例：向侧边栏发送消息，请求翻译
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: '翻译',
    text: text
  });
}

// 处理文本操作（缩写、扩写、润色、修正等）
function handleTextOperation(text: string, operation: string, tabId?: number) {
  console.log(`执行${operation}操作:`, text);
  
  // 示例：向侧边栏发送消息，请求处理文本
  chrome.runtime.sendMessage({
    type: 'PROCESS_TEXT',
    operation: operation,
    text: text
  });
}
