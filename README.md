# ai-chrome-extension

## 开发

```bash
npm run dev # 开发模式，图标是灰色
```

## 发布

### 1. 打包

- 修改package.json中的版本

- 执行打包命令

```bash
npm run package
```

### 2. 修改并上传文件

- 修改内容：修改versions.json中的版本信息

- 上传文件：将versions.json、build目录下的zip文件上传服务器

## 权限

### host_permissions

插件需要访问哪些网站的权限

### permissions

具体的权限列表

- sidePanel：允许使用侧边栏（Side Panel）功能
- storage：允许使用 chrome.storage API 存储和读取数据
- activeTab：允许访问当前的标签页（用户点击插件图标时触发，回调函数中的 tab 就是当前标签页）
- contextMenus：允许插件在右键菜单中添加自定义选项
- scripting：允许动态注入/移除 JavaScript 或 CSS 脚本，需同时声明 "activeTab" 或 "host_permissions"，可以使用 chrome.scripting.xxx API
- tabs：访问浏览器标签页信息（如 URL、标题），操作标签页（创建/刷新/关闭），可以使用 chrome.tabs.xxx API
- cookies：允许插件通过 chrome.cookies API 访问或修改 Cookie。
- alarms：允许设置定时任务（即使扩展休眠也能触发），可使用 chrome.alarms.xxx API
- notifications：使用通知API来显示桌面通知，chrome.notifications.xxx API

## 通信

### sidepanel 和 content-script 通信

使用 `chrome.tabs.sendMessage` 和 `chrome.runtime.onMessage` 进行通信
