<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Assistant 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .test-section h2 {
            color: #007bff;
            margin-top: 0;
        }
        
        .debug-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .status-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }
        
        .test-text {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border: 1px solid #ddd;
            user-select: text;
        }
        
        code {
            background: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
        
        .highlight {
            background: yellow;
            padding: 2px;
        }
        
        ol, ul {
            padding-left: 20px;
        }
        
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Web Assistant 测试页面</h1>
        
        <div class="status-info">
            <strong>✅ 新架构特点：</strong><br>
            • 统一的主容器管理（<code>web-assistant-main-container</code>）<br>
            • 划词工具栏和浮动按钮在同一个 Shadow DOM 中<br>
            • 新的 CSS 类名前缀：<code>web-assistant-xxx</code><br>
            • 修复了划词工具栏一闪而过的问题<br>
            • 改进的事件处理和显示逻辑
        </div>

        <div class="debug-info">
            <strong>🔧 调试步骤：</strong><br>
            1. 打开浏览器开发者工具 (F12)<br>
            2. 查看 Console 标签页<br>
            3. 选择下面的测试文本<br>
            4. 观察控制台输出：应该看到 "WebAssistantManager" 相关日志<br>
            5. 检查 DOM：查找 <code>web-assistant-main-container</code> 元素
        </div>

        <div class="test-section">
            <h2>📝 测试文本 1 - 中文内容</h2>
            <div class="test-text">
                这是一段中文测试文本，用于测试划词工具栏的功能。请选择这段文字中的任意部分，查看是否会出现划词工具栏。工具栏应该包含总结、翻译等功能按钮，并且<span class="highlight">不会一闪而过</span>。
            </div>
            <div class="test-text">
                人工智能技术正在快速发展，它将改变我们的生活方式。机器学习、深度学习、自然语言处理等技术正在各个领域发挥重要作用。请选择这段文字测试AI助手的功能。
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 测试文本 2 - 英文内容</h2>
            <div class="test-text">
                This is an English test paragraph for testing the selection toolbar functionality. Please select any part of this text to see if the selection toolbar appears. The toolbar should contain buttons for summary, translation, and other features.
            </div>
            <div class="test-text">
                Artificial Intelligence is revolutionizing the way we work and live. Machine learning algorithms can process vast amounts of data and provide insights that were previously impossible to obtain. Select this text to test the AI assistant features.
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 测试文本 3 - 混合内容</h2>
            <div class="test-text">
                这是一段包含 <strong>HTML 标签</strong> 和 <em>格式化文本</em> 的测试内容。同时包含一些 <code>代码片段</code> 和 <a href="#" onclick="return false;">链接</a>。请测试选择不同类型的内容，工具栏应该稳定显示。
            </div>
            <div class="test-text">
                Mixed content with <strong>bold text</strong>, <em>italic text</em>, and <code>inline code</code>. The selection toolbar should handle all these elements properly and remain visible until explicitly closed.
            </div>
        </div>

        <div class="test-section">
            <h2>📏 测试文本 4 - 长文本</h2>
            <div class="test-text">
                这是一段较长的测试文本，用于测试工具栏在不同位置的显示效果。当选择的文本位于页面的不同位置时，工具栏应该能够正确定位并显示在选中文本的下方。这段文本足够长，可以测试工具栏在页面边缘的显示情况，以及是否会被页面边界截断。工具栏的位置计算应该考虑到页面滚动和窗口大小的影响。新的架构使用统一的主容器管理，确保所有组件都在同一个 Shadow DOM 中，避免了样式冲突和事件处理问题。
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 预期行为</h2>
            <ul>
                <li><strong>划词工具栏：</strong>选择文本后应该出现，点击关闭按钮或点击其他地方后消失</li>
                <li><strong>浮动按钮：</strong>页面右侧应该有一个可拖拽的蓝色圆形按钮</li>
                <li><strong>Shadow DOM：</strong>所有组件都在 <code>web-assistant-main-container</code> 的 Shadow Root 中</li>
                <li><strong>事件处理：</strong>工具栏不会一闪而过，保持稳定显示</li>
                <li><strong>样式隔离：</strong>插件样式不会影响页面原有样式</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🐛 常见问题排查</h2>
            <ol>
                <li><strong>没有控制台输出：</strong>检查插件是否正确安装和启用</li>
                <li><strong>没有工具栏：</strong>检查 DOM 中是否有 <code>web-assistant-main-container</code></li>
                <li><strong>工具栏一闪而过：</strong>检查事件监听器是否正确绑定</li>
                <li><strong>样式问题：</strong>检查 Shadow DOM 中的样式是否正确加载</li>
                <li><strong>浮动按钮不显示：</strong>检查 z-index 和定位是否正确</li>
            </ol>
        </div>
    </div>

    <script>
        // 调试辅助脚本
        console.log('Web Assistant Test Page loaded');
        
        // 监听选择事件
        document.addEventListener('selectionchange', function() {
            const selection = window.getSelection();
            const selectedText = selection.toString().trim();
            if (selectedText) {
                console.log('Text selected:', selectedText);
            }
        });
        
        // 监听鼠标事件
        document.addEventListener('mouseup', function(e) {
            console.log('Mouse up event at:', e.clientX, e.clientY);
        });
        
        // 检查主容器
        function checkMainContainer() {
            const container = document.querySelector('#web-assistant-main-container');
            if (container) {
                console.log('✅ Main container found:', container);
                console.log('Shadow root:', container.shadowRoot);
            } else {
                console.log('❌ Main container not found');
            }
        }
        
        // 延迟检查，等待插件加载
        setTimeout(checkMainContainer, 2000);
        
        // 定期检查
        setInterval(checkMainContainer, 5000);
    </script>
</body>
</html>
